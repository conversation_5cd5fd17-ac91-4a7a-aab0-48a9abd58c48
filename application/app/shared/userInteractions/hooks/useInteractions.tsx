/** @format */

'use client';

// Extend window interface for interaction caching
declare global {
	interface Window {
		__poiInteractionCache?: Map<
			string,
			{
				data: any;
				timestamp: number;
				loading: Promise<void> | null;
			}
		>;
	}
}

import {
	InteractionCounts,
	UserInteractionStates,
} from '@/app/shared/userInteractions/shared/types';
import { POIIdentifier } from '@/app/shared/userInteractions/shared/types/base';
import { useSession } from 'next-auth/react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
// Individual hooks - keeping for now, can be optimized later
import { useFavorites } from '@/app/shared/userInteractions/favorites/hooks/useFavorites';
import { useLikes } from '@/app/shared/userInteractions/likes/hooks/useLikes';
import { useVisits } from '@/app/shared/userInteractions/visits/hooks/useVisits';
// TODO: Add reviews when reviews module is completed
// import { useReviews } from '@/app/shared/userInteractions/reviews/hooks'

interface UseInteractionsOptions {
	poi_identifier: POIIdentifier;
	auto_load?: boolean;
	enable_optimistic_updates?: boolean;
	loading_strategy?: 'batch' | 'individual' | 'none';
	initialData?: {
		like_count: number;
		favorite_count: number;
		visit_count: number;
		user_has_liked: boolean;
		user_has_favorited: boolean;
		user_has_visited: boolean;
	};
}

interface UseInteractionsResult {
	// Aggregated state
	counts: InteractionCounts;
	states: UserInteractionStates;
	loading: boolean;
	loadingStates: {
		likes: boolean;
		visits: boolean;
		favorites: boolean; // add favorites
	};
	readyStates: {
		likes: boolean;
		visits: boolean;
		favorites: boolean; // add favorites
	};
	error: string | null;

	// Individual interaction actions
	likes: {
		toggle: () => Promise<void>;
		add: () => Promise<void>;
		remove: () => Promise<void>;
	};
	visits: {
		add: (visitData?: Record<string, unknown>) => Promise<void>;
		remove: (visitId: string | number) => Promise<void>;
		toggle: (visitData?: Record<string, unknown>) => Promise<void>;
	};
	favorites: {
		add: (favoriteData?: Record<string, unknown>) => Promise<void>;
		remove: (favoriteId: string | number) => Promise<void>;
		toggle: (favoriteData?: Record<string, unknown>) => Promise<void>;
	};
	// reviews: {
	//   add: (reviewData: any) => Promise<void>
	//   update: (reviewId: string, reviewData: any) => Promise<void>
	//   remove: (reviewId: string) => Promise<void>
	// }

	// Utility actions
	refresh: () => Promise<void>;
}

/**
 * Unified batched interaction loader that gets both total counts and user states in a single API call
 * This prevents redundant API calls and improves performance
 */
const useUnifiedBatchedLoader = (
	poi_identifier: POIIdentifier,
	userId?: string,
	auto_load: boolean = true,
	initialData?: {
		like_count: number;
		favorite_count: number;
		visit_count: number;
		user_has_liked: boolean;
		user_has_favorited: boolean;
		user_has_visited: boolean;
	}
) => {
	const [batchedData, setBatchedData] = useState<{
		likes: { count: number; isLiked: boolean } | null;
		visits: { count: number; hasVisited: boolean } | null;
		favorites: { count: number; isFavorited: boolean } | null;
	}>(() => {
		if (initialData) {
			return {
				likes: {
					count: initialData.like_count,
					isLiked: initialData.user_has_liked,
				},
				visits: {
					count: initialData.visit_count,
					hasVisited: initialData.user_has_visited,
				},
				favorites: {
					count: initialData.favorite_count,
					isFavorited: initialData.user_has_favorited,
				},
			};
		}
		return {
			likes: null,
			visits: null,
			favorites: null,
		};
	});
	const [userStates, setUserStates] = useState<{
		isLiked: boolean;
		hasVisited: boolean;
		isFavorited: boolean;
	}>(() => {
		if (initialData) {
			return {
				isLiked: initialData.user_has_liked,
				hasVisited: initialData.user_has_visited,
				isFavorited: initialData.user_has_favorited,
			};
		}
		return {
			isLiked: false,
			hasVisited: false,
			isFavorited: false,
		};
	});
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const loadingRef = useRef(false);

	const loadAllInteractions = useCallback(async () => {
		if (!poi_identifier || loadingRef.current) return;

		// Skip API call if initial data is provided
		if (initialData) {
			return;
		}

		// Create cache key for this POI
		const cacheKey = `${poi_identifier.poi_type}-${
			poi_identifier.poi_id ||
			poi_identifier.user_poi_temp_id ||
			poi_identifier.user_poi_approved_id
		}`;

		// Initialize global cache if needed
		if (typeof window !== 'undefined' && !window.__poiInteractionCache) {
			window.__poiInteractionCache = new Map();
		}

		// Check if we already have cached data (within 10 seconds) or ongoing request
		if (typeof window !== 'undefined' && window.__poiInteractionCache) {
			const cached = window.__poiInteractionCache.get(cacheKey);
			if (cached) {
				const isRecent = Date.now() - cached.timestamp < 10000; // 10 seconds

				// If there's already a loading request for this POI, wait for it
				if (cached.loading) {
					console.log(`🔄 Waiting for existing request for POI ${cacheKey}`);
					try {
						await cached.loading;
						// After waiting, use the cached data
						const updatedCached = window.__poiInteractionCache.get(cacheKey);
						if (updatedCached?.data) {
							setBatchedData({
								likes: {
									count: updatedCached.data.likes?.count || 0,
									isLiked: updatedCached.data.likes?.isLiked || false,
								},
								visits: {
									count: updatedCached.data.visits?.count || 0,
									hasVisited: updatedCached.data.visits?.hasVisited || false,
								},
								favorites: {
									count: updatedCached.data.favorites?.count || 0,
									isFavorited:
										updatedCached.data.favorites?.isFavorited || false,
								},
							});
							setUserStates({
								isLiked: updatedCached.data.likes?.isLiked || false,
								hasVisited: updatedCached.data.visits?.hasVisited || false,
								isFavorited: updatedCached.data.favorites?.isFavorited || false,
							});
						}
						return;
					} catch (err) {
						console.log(
							`❌ Existing request failed for POI ${cacheKey}, making new request`
						);
						// If the other request failed, continue with our own request
					}
				}

				// Use recent cached data
				if (isRecent && cached.data) {
					console.log(`✅ Using cached data for POI ${cacheKey}`);
					setBatchedData({
						likes: {
							count: cached.data.likes?.count || 0,
							isLiked: cached.data.likes?.isLiked || false,
						},
						visits: {
							count: cached.data.visits?.count || 0,
							hasVisited: cached.data.visits?.hasVisited || false,
						},
						favorites: {
							count: cached.data.favorites?.count || 0,
							isFavorited: cached.data.favorites?.isFavorited || false,
						},
					});
					setUserStates({
						isLiked: cached.data.likes?.isLiked || false,
						hasVisited: cached.data.visits?.hasVisited || false,
						isFavorited: cached.data.favorites?.isFavorited || false,
					});
					return;
				}
			}
		}

		console.log(`🌐 Making new API request for POI ${cacheKey}`);
		loadingRef.current = true;
		setLoading(true);
		setError(null);

		// Create a promise for this loading operation
		let loadingPromise: Promise<void>;

		const executeLoad = async () => {
			try {
				// Make a single API call to get both total counts and user states using unified API
				const params = new URLSearchParams();
				if (poi_identifier.poi_id)
					params.append('poiId', poi_identifier.poi_id.toString());
				if (poi_identifier.user_poi_temp_id)
					params.append(
						'userPoiTempId',
						poi_identifier.user_poi_temp_id.toString()
					);
				if (poi_identifier.user_poi_approved_id)
					params.append(
						'userPoiApprovedId',
						poi_identifier.user_poi_approved_id.toString()
					);
				if (poi_identifier.poi_type)
					params.append('poiType', poi_identifier.poi_type);

				// Include userId if available to get user states along with total counts
				if (userId) {
					params.append('userId', userId);
				}

				// Use batch mode in unified API
				params.append('batch', 'true');

				const response = await fetch(
					`/api/pois/interactions?${params.toString()}`
				);
				const data = await response.json();

				if (data.success) {
					// Set both total counts and user states from the single API response
					setBatchedData({
						likes: {
							count: data.likes?.count || 0,
							isLiked: data.likes?.isLiked || false,
						},
						visits: {
							count: data.visits?.count || 0,
							hasVisited: data.visits?.hasVisited || false,
						},
						favorites: {
							count: data.favorites?.count || 0,
							isFavorited: data.favorites?.isFavorited || false,
						},
					});

					setUserStates({
						isLiked: data.likes?.isLiked || false,
						hasVisited: data.visits?.hasVisited || false,
						isFavorited: data.favorites?.isFavorited || false,
					});

					// Cache the successful response
					if (typeof window !== 'undefined' && window.__poiInteractionCache) {
						window.__poiInteractionCache.set(cacheKey, {
							data: data,
							timestamp: Date.now(),
							loading: null,
						});
						console.log(`💾 Cached data for POI ${cacheKey}`);
					}
				} else {
					throw new Error(data.error || 'Failed to load interactions');
				}
			} catch (err) {
				console.error('Error loading unified batch interactions:', err);
				setError(
					err instanceof Error ? err.message : 'Failed to load interactions'
				);
				// Set default values on error
				setBatchedData({
					likes: { count: 0, isLiked: false },
					visits: { count: 0, hasVisited: false },
					favorites: { count: 0, isFavorited: false },
				});
				setUserStates({
					isLiked: false,
					hasVisited: false,
					isFavorited: false,
				});
			} finally {
				setLoading(false);
				loadingRef.current = false;
				// Clear loading promise from cache
				if (typeof window !== 'undefined' && window.__poiInteractionCache) {
					const cached = window.__poiInteractionCache.get(cacheKey);
					if (cached && cached.loading === loadingPromise) {
						window.__poiInteractionCache.set(cacheKey, {
							...cached,
							loading: null,
						});
					}
				}
			}
		};

		loadingPromise = executeLoad();

		// Store the loading promise in cache to prevent duplicate requests
		if (typeof window !== 'undefined' && window.__poiInteractionCache) {
			window.__poiInteractionCache.set(cacheKey, {
				data: null,
				timestamp: Date.now(),
				loading: loadingPromise,
			});
		}

		// Wait for the loading promise to complete
		await loadingPromise;
	}, [poi_identifier, userId]);

	useEffect(() => {
		if (auto_load) {
			loadAllInteractions();
		}
	}, [auto_load, loadAllInteractions]);

	return {
		batchedData,
		userStates,
		loading,
		error,
		refresh: loadAllInteractions,
		// Expose setters for optimistic updates
		setBatchedData,
		setUserStates,
	};
};

/**
 * Master hook that combines all interaction types for a POI
 * Provides a unified interface for all user interactions
 */
export const useInteractions = ({
	poi_identifier,
	auto_load = true,
	enable_optimistic_updates = true,
	loading_strategy = 'batch',
	initialData,
}: UseInteractionsOptions): UseInteractionsResult => {
	const { data: session } = useSession();
	const userId = session?.user?.id;

	// Determine loading approach based on strategy
	const useBatchLoading = loading_strategy === 'batch';
	const useIndividualLoading = loading_strategy === 'individual';

	// Use a single batched loader that gets both total counts and user states in one call
	const {
		batchedData,
		userStates,
		loading: batchLoading,
		error: batchError,
		refresh: refreshBatch,
		setBatchedData,
		setUserStates,
	} = useUnifiedBatchedLoader(
		poi_identifier,
		userId,
		auto_load && useBatchLoading,
		initialData
	);

	// Use individual interaction hooks ONLY when not using batch loading
	// Always auto-load when using individual loading strategy to ensure hooks initialize properly
	const shouldAutoLoad =
		useIndividualLoading || (auto_load && useIndividualLoading);

	// Completely disable individual hooks when using batch loading
	const likesHook = useBatchLoading
		? {
				likeCount: 0,
				isLiked: false,
				loading: false,
				actionLoading: false,
				error: null,
				ready: false,
				addLike: async () => {},
				removeLike: async () => {},
				toggleLike: async () => {},
				refresh: async () => {},
		  }
		: useLikes({
				poi_identifier,
				user_id: userId,
				auto_load: shouldAutoLoad,
				enable_optimistic_updates,
		  });

	const visitsHook = useBatchLoading
		? {
				visitCount: 0,
				hasVisited: false,
				loading: false,
				actionLoading: false,
				error: null,
				ready: false,
				visits: [],
				addVisit: async () => {},
				removeVisit: async () => {},
				toggleVisit: async () => {},
				refresh: async () => {},
		  }
		: useVisits({
				poi_identifier,
				user_id: userId,
				auto_load: shouldAutoLoad,
				enable_optimistic_updates,
		  });

	const favoritesHook = useBatchLoading
		? {
				favoriteCount: 0,
				isFavorited: false,
				loading: false,
				actionLoading: false,
				error: null,
				ready: false,
				favorites: [],
				addFavorite: async () => {},
				removeFavorite: async () => {},
				toggleFavorite: async () => {},
				refresh: async () => {},
		  }
		: useFavorites({
				poi_identifier,
				user_id: userId,
				auto_load: shouldAutoLoad,
				enable_optimistic_updates,
		  });

	// Aggregate state - combine batched and individual hook errors
	const [error, setError] = React.useState<string | null>(null);

	// Update error state when batch error or individual hook errors change
	useEffect(() => {
		const errors = [
			batchError,
			likesHook.error,
			visitsHook.error,
			favoritesHook.error,
		].filter(Boolean);

		if (errors.length > 0) {
			setError(errors[0]); // Show the first error
		} else {
			setError(null);
		}
	}, [batchError, likesHook.error, visitsHook.error, favoritesHook.error]);

	// Simplified approach - use data source based on loading strategy only

	// Aggregate counts - use appropriate data source based on loading strategy
	const counts: InteractionCounts = {
		like_count: useBatchLoading
			? batchedData.likes?.count ?? 0
			: useIndividualLoading
			? likesHook.likeCount
			: likesHook.likeCount,
		visit_count: useBatchLoading
			? batchedData.visits?.count ?? 0
			: useIndividualLoading
			? visitsHook.visitCount
			: visitsHook.visitCount,
		favorite_count: useBatchLoading
			? batchedData.favorites?.count ?? 0
			: useIndividualLoading
			? favoritesHook.favoriteCount
			: favoritesHook.favoriteCount,
		share_count: 0, // Not implemented yet
		review_count: 0, // Not implemented yet
	};

	// Aggregate states - use appropriate data source based on loading strategy
	const states: UserInteractionStates = {
		isLiked: useBatchLoading
			? userStates.isLiked
			: useIndividualLoading
			? likesHook.isLiked
			: likesHook.isLiked,
		hasVisited: useBatchLoading
			? userStates.hasVisited
			: useIndividualLoading
			? visitsHook.hasVisited
			: visitsHook.hasVisited,
		isFavorited: useBatchLoading
			? userStates.isFavorited
			: useIndividualLoading
			? favoritesHook.isFavorited
			: favoritesHook.isFavorited,
		hasReviewed: false, // Not implemented yet
		hasShared: false, // Not implemented yet
	};

	// Aggregate loading based on strategy
	const loading = useBatchLoading
		? batchLoading
		: useIndividualLoading
		? likesHook.loading || visitsHook.loading || favoritesHook.loading
		: false;

	const loadingStates = {
		likes: likesHook.actionLoading, // Only show action loading for individual operations
		visits: visitsHook.actionLoading,
		favorites: favoritesHook.actionLoading,
	};

	const readyStates = {
		likes: useIndividualLoading
			? likesHook.ready
			: useBatchLoading
			? !batchLoading && batchedData.likes !== undefined
			: likesHook.ready,
		visits: useIndividualLoading
			? visitsHook.ready
			: useBatchLoading
			? !batchLoading && batchedData.visits !== undefined
			: visitsHook.ready,
		favorites: useIndividualLoading
			? favoritesHook.ready
			: useBatchLoading
			? !batchLoading && batchedData.favorites !== undefined
			: favoritesHook.ready,
	};

	// Single API call function for user interactions when using batch loading
	const callSingleInteractionAPI = React.useCallback(
		async (
			interactionType: 'like' | 'favorite' | 'visit',
			action: 'add' | 'remove'
		) => {
			if (!poi_identifier) throw new Error('POI identifier is required');

			// Build correct body parameters based on POI type
			const body: Record<string, unknown> = {
				poiType: poi_identifier.poi_type,
				interactionType,
				action,
			};

			// Add the correct POI ID field based on POI type
			if (poi_identifier.poi_type === 'official' && poi_identifier.poi_id) {
				body.poiId = poi_identifier.poi_id;
			} else if (
				poi_identifier.poi_type === 'user_temp' &&
				poi_identifier.user_poi_temp_id
			) {
				body.userPoiTempId = poi_identifier.user_poi_temp_id;
			} else if (
				poi_identifier.poi_type === 'user_approved' &&
				poi_identifier.user_poi_approved_id
			) {
				body.userPoiApprovedId = poi_identifier.user_poi_approved_id;
			}

			const response = await fetch('/api/pois/interactions', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(body),
			});

			if (!response.ok) {
				// Handle error cases (daily limits removed for interactions)
				const errorData = await response.json().catch(() => ({}));
				throw new Error(
					errorData.error || `Failed to ${action} ${interactionType}`
				);
			}

			return response.json();
		},
		[poi_identifier]
	);

	// Action handlers - use individual hooks for individual strategy, direct API calls for batch strategy
	const likes = useBatchLoading
		? {
				toggle: async () => {
					if (!userId) throw new Error('User must be logged in');
					const currentIsLiked = userStates.isLiked;
					const action = currentIsLiked ? 'remove' : 'add';

					// Optimistic update
					const newIsLiked = !currentIsLiked;
					const newCount = currentIsLiked
						? Math.max(0, (batchedData.likes?.count || 0) - 1)
						: (batchedData.likes?.count || 0) + 1;

					setUserStates((prev) => ({ ...prev, isLiked: newIsLiked }));
					setBatchedData((prev) => ({
						...prev,
						likes: { count: newCount, isLiked: newIsLiked },
					}));

					try {
						await callSingleInteractionAPI('like', action);
						// No need to refresh - optimistic update already applied
					} catch (error) {
						// Revert optimistic update on error
						setUserStates((prev) => ({ ...prev, isLiked: currentIsLiked }));
						setBatchedData((prev) => ({
							...prev,
							likes: {
								count: batchedData.likes?.count || 0,
								isLiked: currentIsLiked,
							},
						}));
						throw error;
					}
				},
				add: async () => {
					if (!userId) throw new Error('User must be logged in');
					if (userStates.isLiked) return; // Already liked

					// Optimistic update
					const newCount = (batchedData.likes?.count || 0) + 1;
					setUserStates((prev) => ({ ...prev, isLiked: true }));
					setBatchedData((prev) => ({
						...prev,
						likes: { count: newCount, isLiked: true },
					}));

					try {
						await callSingleInteractionAPI('like', 'add');
					} catch (error) {
						// Revert optimistic update on error
						setUserStates((prev) => ({ ...prev, isLiked: false }));
						setBatchedData((prev) => ({
							...prev,
							likes: { count: batchedData.likes?.count || 0, isLiked: false },
						}));
						throw error;
					}
				},
				remove: async () => {
					if (!userId) throw new Error('User must be logged in');
					if (!userStates.isLiked) return; // Already not liked

					// Optimistic update
					const newCount = Math.max(0, (batchedData.likes?.count || 0) - 1);
					setUserStates((prev) => ({ ...prev, isLiked: false }));
					setBatchedData((prev) => ({
						...prev,
						likes: { count: newCount, isLiked: false },
					}));

					try {
						await callSingleInteractionAPI('like', 'remove');
					} catch (error) {
						// Revert optimistic update on error
						setUserStates((prev) => ({ ...prev, isLiked: true }));
						setBatchedData((prev) => ({
							...prev,
							likes: { count: batchedData.likes?.count || 0, isLiked: true },
						}));
						throw error;
					}
				},
		  }
		: {
				toggle: likesHook.toggleLike,
				add: likesHook.addLike,
				remove: likesHook.removeLike,
		  };

	const visits = useBatchLoading
		? {
				toggle: async () => {
					if (!userId) throw new Error('User must be logged in');
					const currentHasVisited = userStates.hasVisited;
					const action = currentHasVisited ? 'remove' : 'add';

					// Optimistic update
					const newHasVisited = !currentHasVisited;
					const newCount = currentHasVisited
						? Math.max(0, (batchedData.visits?.count || 0) - 1)
						: (batchedData.visits?.count || 0) + 1;

					setUserStates((prev) => ({ ...prev, hasVisited: newHasVisited }));
					setBatchedData((prev) => ({
						...prev,
						visits: { count: newCount, hasVisited: newHasVisited },
					}));

					try {
						await callSingleInteractionAPI('visit', action);
						// No need to refresh - optimistic update already applied
					} catch (error) {
						// Revert optimistic update on error
						setUserStates((prev) => ({
							...prev,
							hasVisited: currentHasVisited,
						}));
						setBatchedData((prev) => ({
							...prev,
							visits: {
								count: batchedData.visits?.count || 0,
								hasVisited: currentHasVisited,
							},
						}));
						throw error;
					}
				},
				add: async () => {
					if (!userId) throw new Error('User must be logged in');
					if (userStates.hasVisited) return; // Already visited

					// Optimistic update
					const newCount = (batchedData.visits?.count || 0) + 1;
					setUserStates((prev) => ({ ...prev, hasVisited: true }));
					setBatchedData((prev) => ({
						...prev,
						visits: { count: newCount, hasVisited: true },
					}));

					try {
						await callSingleInteractionAPI('visit', 'add');
					} catch (error) {
						// Revert optimistic update on error
						setUserStates((prev) => ({ ...prev, hasVisited: false }));
						setBatchedData((prev) => ({
							...prev,
							visits: {
								count: batchedData.visits?.count || 0,
								hasVisited: false,
							},
						}));
						throw error;
					}
				},
				remove: async () => {
					if (!userId) throw new Error('User must be logged in');
					if (!userStates.hasVisited) return; // Already not visited

					// Optimistic update
					const newCount = Math.max(0, (batchedData.visits?.count || 0) - 1);
					setUserStates((prev) => ({ ...prev, hasVisited: false }));
					setBatchedData((prev) => ({
						...prev,
						visits: { count: newCount, hasVisited: false },
					}));

					try {
						await callSingleInteractionAPI('visit', 'remove');
					} catch (error) {
						// Revert optimistic update on error
						setUserStates((prev) => ({ ...prev, hasVisited: true }));
						setBatchedData((prev) => ({
							...prev,
							visits: {
								count: batchedData.visits?.count || 0,
								hasVisited: true,
							},
						}));
						throw error;
					}
				},
		  }
		: {
				add: visitsHook.addVisit,
				remove: visitsHook.removeVisit,
				toggle: visitsHook.toggleVisit,
		  };

	const favorites = useBatchLoading
		? {
				toggle: async () => {
					if (!userId) throw new Error('User must be logged in');
					const currentIsFavorited = userStates.isFavorited;
					const action = currentIsFavorited ? 'remove' : 'add';

					// Optimistic update
					const newIsFavorited = !currentIsFavorited;
					const newCount = currentIsFavorited
						? Math.max(0, (batchedData.favorites?.count || 0) - 1)
						: (batchedData.favorites?.count || 0) + 1;

					setUserStates((prev) => ({ ...prev, isFavorited: newIsFavorited }));
					setBatchedData((prev) => ({
						...prev,
						favorites: { count: newCount, isFavorited: newIsFavorited },
					}));

					try {
						await callSingleInteractionAPI('favorite', action);
						// No need to refresh - optimistic update already applied
					} catch (error) {
						// Revert optimistic update on error
						setUserStates((prev) => ({
							...prev,
							isFavorited: currentIsFavorited,
						}));
						setBatchedData((prev) => ({
							...prev,
							favorites: {
								count: batchedData.favorites?.count || 0,
								isFavorited: currentIsFavorited,
							},
						}));
						throw error;
					}
				},
				add: async () => {
					if (!userId) throw new Error('User must be logged in');
					if (userStates.isFavorited) return; // Already favorited

					// Optimistic update
					const newCount = (batchedData.favorites?.count || 0) + 1;
					setUserStates((prev) => ({ ...prev, isFavorited: true }));
					setBatchedData((prev) => ({
						...prev,
						favorites: { count: newCount, isFavorited: true },
					}));

					try {
						await callSingleInteractionAPI('favorite', 'add');
					} catch (error) {
						// Revert optimistic update on error
						setUserStates((prev) => ({ ...prev, isFavorited: false }));
						setBatchedData((prev) => ({
							...prev,
							favorites: {
								count: batchedData.favorites?.count || 0,
								isFavorited: false,
							},
						}));
						throw error;
					}
				},
				remove: async () => {
					if (!userId) throw new Error('User must be logged in');
					if (!userStates.isFavorited) return; // Already not favorited

					// Optimistic update
					const newCount = Math.max(0, (batchedData.favorites?.count || 0) - 1);
					setUserStates((prev) => ({ ...prev, isFavorited: false }));
					setBatchedData((prev) => ({
						...prev,
						favorites: { count: newCount, isFavorited: false },
					}));

					try {
						await callSingleInteractionAPI('favorite', 'remove');
					} catch (error) {
						// Revert optimistic update on error
						setUserStates((prev) => ({ ...prev, isFavorited: true }));
						setBatchedData((prev) => ({
							...prev,
							favorites: {
								count: batchedData.favorites?.count || 0,
								isFavorited: true,
							},
						}));
						throw error;
					}
				},
		  }
		: {
				toggle: favoritesHook.toggleFavorite,
				add: favoritesHook.addFavorite,
				remove: favoritesHook.removeFavorite,
		  };

	// Refresh all interactions based on strategy
	const refresh = React.useCallback(async () => {
		try {
			if (useBatchLoading) {
				// Use unified batch refresh for batch strategy
				await refreshBatch();
				// If batched refresh fails, fallback to individual refreshes
				if (batchError) {
					await Promise.all([
						likesHook.refresh(),
						visitsHook.refresh(),
						favoritesHook.refresh(),
					]);
				}
			} else if (useIndividualLoading) {
				// Use individual refreshes for individual strategy
				await Promise.all([
					likesHook.refresh(),
					visitsHook.refresh(),
					favoritesHook.refresh(),
				]);
			}
			// For 'none' strategy, don't refresh anything
			setError(null);
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to refresh interactions';
			setError(errorMessage);
		}
	}, [
		useBatchLoading,
		useIndividualLoading,
		refreshBatch,
		batchError,
		likesHook.refresh,
		visitsHook.refresh,
		favoritesHook.refresh,
	]);

	// The individual hooks already handle auto-loading, so we don't need to trigger refresh here

	return {
		counts,
		states,
		loading,
		loadingStates, // for button disables
		readyStates, // for robust state checks
		error,
		likes,
		visits,
		favorites,
		// reviews,
		refresh,
	};
};

export default useInteractions;
