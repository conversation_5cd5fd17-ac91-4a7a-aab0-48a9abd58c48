/** @format */

'use client';

import { FavoritesService } from '@/app/shared/userInteractions/favorites/services';
import {
	FavoriteWithUser,
	POIFavoriteInteraction,
	UseFavoritesOptions,
	UseFavoritesResult,
	UseSavesOptions,
	UseSavesResult,
	UseUserFavoritesOptions,
	UseUserFavoritesResult,
} from '@/app/shared/userInteractions/favorites/types';
import { POIIdentifier } from '@/app/shared/userInteractions/shared/types';
import { useSession } from 'next-auth/react';
import { useCallback, useEffect, useState } from 'react';

// Main favorites hook (new API)
export const useFavorites = ({
	poi_identifier,
	user_id,
	auto_load = true,
	enable_optimistic_updates = true,
	sort_by = 'created_at',
	sort_order = 'desc',
	limit = 20,
	favorite_type,
}: UseFavoritesOptions = {}): UseFavoritesResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State
	const [favorites, setFavorites] = useState<FavoriteWithUser[]>([]);
	const [userFavorite, setUserFavorite] =
		useState<POIFavoriteInteraction | null>(null);
	const [favoriteCount, setFavoriteCount] = useState(0);
	const [isFavorited, setIsFavorited] = useState(false);
	const [loading, setLoading] = useState(false);
	const [actionLoading, setActionLoading] = useState(false);
	const [ready, setReady] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(false);
	const [currentOffset, setCurrentOffset] = useState(0);

	// Load favorite state for the POI (simplified like likes)
	const loadFavoriteState = useCallback(async () => {
		if (!poi_identifier) return;

		setLoading(true);
		setError(null);

		try {
			if (currentUserId) {
				// Load user-specific favorite status
				const response = await FavoritesService.getUserFavoriteStatus(
					poi_identifier,
					currentUserId
				);

				if (response.success) {
					setIsFavorited(response.isFavorited);
					setFavoriteCount(response.favoriteCount || 0);
					setReady(true);
					console.log('Loaded favorite state:', {
						isFavorited: response.isFavorited,
						favoriteCount: response.favoriteCount,
					});
				} else {
					throw new Error('Failed to load favorite status');
				}
			} else {
				// Load public favorite count only (no user authentication)
				const response = await FavoritesService.getPOIFavoriteCount(
					poi_identifier
				);

				if (response.success) {
					setIsFavorited(false); // No user, so not favorited
					setFavoriteCount(response.favoriteCount || 0);
					setReady(true);
					console.log('Loaded public favorite count:', response.favoriteCount);
				} else {
					throw new Error('Failed to load favorite count');
				}
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to load favorite data';
			setError(errorMessage);
			console.error('Error loading favorite state:', err);
		} finally {
			setLoading(false);
		}
	}, [poi_identifier, currentUserId]);

	// Load favorites for the POI
	const loadFavorites = useCallback(
		async (resetOffset: boolean = false) => {
			if (!poi_identifier) return;
			setLoading(true);
			setError(null);
			const offsetToUse = resetOffset ? 0 : currentOffset;
			try {
				if (currentUserId) {
					// Load user-specific favorite status and count
					const userFavResp = await FavoritesService.getUserFavoriteForPOI(
						poi_identifier,
						currentUserId
					);
					setUserFavorite(userFavResp.favorite);
					setIsFavorited(userFavResp.isFavorited);
					// Load count
					const favResp = await FavoritesService.getPOIFavorites(
						poi_identifier,
						{
							limit,
							offset: offsetToUse,
							sortBy: sort_by,
							sortOrder: sort_order,
							favorite_type,
						}
					);
					if (favResp.success) {
						const favoriteInteractions = (favResp.interactions ||
							[]) as POIFavoriteInteraction[];

						if (resetOffset || offsetToUse === 0) {
							setFavorites(favoriteInteractions);
						} else {
							setFavorites((prev) => [...prev, ...favoriteInteractions]);
						}
						setFavoriteCount(favResp.total_count || 0);
						setHasMore(favResp.has_more || false);
						setCurrentOffset(offsetToUse + favoriteInteractions.length);
					} else {
						throw new Error(favResp.error || 'Failed to load favorites');
					}
				} else {
					// Unauthenticated: only load public favorite count
					setIsFavorited(false);
					setUserFavorite(null);
					const favResp = await FavoritesService.getPOIFavorites(
						poi_identifier,
						{
							limit,
							offset: offsetToUse,
							sortBy: sort_by,
							sortOrder: sort_order,
							favorite_type,
						}
					);
					if (favResp.success) {
						const favoriteInteractions = (favResp.interactions ||
							[]) as POIFavoriteInteraction[];

						if (resetOffset || offsetToUse === 0) {
							setFavorites(favoriteInteractions);
						} else {
							setFavorites((prev) => [...prev, ...favoriteInteractions]);
						}
						setFavoriteCount(favResp.total_count || 0);
						setHasMore(favResp.has_more || false);
						setCurrentOffset(offsetToUse + favoriteInteractions.length);
					} else {
						throw new Error(favResp.error || 'Failed to load favorites');
					}
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to load favorites';
				setError(errorMessage);
				console.error('Error loading favorites:', err);
			} finally {
				setLoading(false);
				setReady(true);
			}
		},
		[
			poi_identifier,
			currentUserId,
			currentOffset,
			limit,
			sort_by,
			sort_order,
			favorite_type,
		]
	);

	// Load user's favorite for this POI
	const loadUserFavorite = useCallback(async () => {
		if (!poi_identifier || !currentUserId) {
			setUserFavorite(null);
			setIsFavorited(false);
			return;
		}

		try {
			const result = await FavoritesService.getUserFavoriteForPOI(
				poi_identifier,
				currentUserId
			);
			setUserFavorite(result.favorite);
			setIsFavorited(result.isFavorited);
		} catch (err) {
			console.error('Error loading user favorite:', err);
			setUserFavorite(null);
			setIsFavorited(false);
		}
	}, [poi_identifier, currentUserId]);

	// Add a favorite
	const addFavorite = useCallback(async () => {
		if (!poi_identifier || !currentUserId) {
			setError('POI identifier and user ID are required');
			return;
		}
		if (!ready || actionLoading) {
			console.log('Not ready or already loading, skipping');
			return;
		}
		setActionLoading(true);
		setError(null);
		if (enable_optimistic_updates) {
			setIsFavorited(true);
			setFavoriteCount((prev) => prev + 1);
		}
		try {
			const response = await FavoritesService.addFavorite(poi_identifier);
			if (response.success) {
				if (!enable_optimistic_updates) {
					setIsFavorited(true);
					setFavoriteCount(response.favorite_count || favoriteCount + 1);
				}
				// Don't auto-refresh to prevent duplicate requests
				// The optimistic update already handles the UI state
			} else {
				throw new Error(response.error || 'Failed to add favorite');
			}
		} catch (err) {
			if (enable_optimistic_updates) {
				setIsFavorited(false);
				setFavoriteCount((prev) => Math.max(0, prev - 1));
			}
			setError(err instanceof Error ? err.message : 'Failed to add favorite');
			console.error('Error adding favorite:', err);
		} finally {
			setActionLoading(false);
		}
	}, [
		poi_identifier,
		currentUserId,
		ready,
		actionLoading,
		isFavorited,
		favoriteCount,
		enable_optimistic_updates,
		loadFavoriteState,
	]);

	// Remove a favorite
	const removeFavorite = useCallback(async () => {
		if (!poi_identifier || !currentUserId) {
			setError('POI identifier and user ID are required');
			return;
		}
		if (!ready || actionLoading) {
			console.log('Not ready or already loading, skipping');
			return;
		}
		setActionLoading(true);
		setError(null);
		if (enable_optimistic_updates) {
			setIsFavorited(false);
			setFavoriteCount((prev) => Math.max(0, prev - 1));
		}
		try {
			const response = await FavoritesService.removeFavorite(poi_identifier);
			if (response.success) {
				if (!enable_optimistic_updates) {
					setIsFavorited(false);
					setFavoriteCount(
						response.favorite_count || Math.max(0, favoriteCount - 1)
					);
				}
				// Don't auto-refresh to prevent duplicate requests
				// The optimistic update already handles the UI state
			} else {
				throw new Error(response.error || 'Failed to remove favorite');
			}
		} catch (err) {
			if (enable_optimistic_updates) {
				setIsFavorited(true);
				setFavoriteCount((prev) => prev + 1);
			}
			setError(
				err instanceof Error ? err.message : 'Failed to remove favorite'
			);
			console.error('Error removing favorite:', err);
		} finally {
			setActionLoading(false);
		}
	}, [
		poi_identifier,
		currentUserId,
		ready,
		actionLoading,
		isFavorited,
		favoriteCount,
		enable_optimistic_updates,
		loadFavoriteState,
	]);

	// Toggle favorite
	const toggleFavorite = useCallback(async () => {
		if (!ready || actionLoading) {
			console.log('Not ready or already loading, skipping toggle');
			return;
		}
		if (isFavorited) {
			await removeFavorite();
		} else {
			await addFavorite();
		}
	}, [
		ready,
		actionLoading,
		isFavorited,
		userFavorite,
		addFavorite,
		removeFavorite,
	]);

	// Load more favorites
	const loadMore = useCallback(async () => {
		if (!hasMore || loading) return;
		await loadFavorites(false);
	}, [hasMore, loading, loadFavorites]);

	// Refresh all data
	const refresh = useCallback(async () => {
		setCurrentOffset(0);
		await Promise.all([loadFavorites(true), loadUserFavorite()]);
	}, [loadFavorites, loadUserFavorite]);

	// Initial load effect (like useVisits)
	useEffect(() => {
		let cancelled = false;
		if (!poi_identifier) {
			setLoading(false);
			setReady(false);
			setError('POI identifier is missing.');
			return;
		}

		// Only auto-load if auto_load is enabled
		if (!auto_load) {
			setLoading(false);
			setReady(false);
			return;
		}

		setLoading(true);
		setReady(false);
		setError(null);
		const loadAll = async () => {
			try {
				// Single API call to get both user status and total count
				if (currentUserId) {
					// Get user-specific status (includes total count)
					const response = await FavoritesService.getUserFavoriteStatus(
						poi_identifier,
						currentUserId
					);
					if (response.success) {
						setIsFavorited(response.isFavorited);
						setFavoriteCount(response.favoriteCount || 0);
					} else {
						// Fallback: get public count only
						setIsFavorited(false);
						const countResponse = await FavoritesService.getPOIFavoriteCount(
							poi_identifier
						);
						setFavoriteCount(
							countResponse.success ? countResponse.favoriteCount || 0 : 0
						);
					}
				} else {
					// Get public count only (no user session)
					const response = await FavoritesService.getPOIFavoriteCount(
						poi_identifier
					);
					if (response.success) {
						setIsFavorited(false);
						setFavoriteCount(response.favoriteCount || 0);
					} else {
						setIsFavorited(false);
						setFavoriteCount(0);
					}
				}
			} catch (error) {
				console.error('Error loading favorites:', error);
				if (!cancelled) {
					setError('Failed to load favorites');
					// Set default values on error
					setIsFavorited(false);
					setFavoriteCount(0);
				}
			} finally {
				if (!cancelled) {
					setLoading(false);
					setReady(true); // Always set ready to true in finally block like visits hook
				}
			}
		};
		loadAll();
		return () => {
			cancelled = true;
		};
	}, [poi_identifier, currentUserId, auto_load]);

	// Note: Removed duplicate auto_load effect since we now have the initial load effect that works like useVisits

	return {
		favorites,
		userFavorite,
		favoriteCount,
		isFavorited,
		loading,
		actionLoading,
		ready,
		error,
		hasMore,
		addFavorite,
		removeFavorite,
		toggleFavorite,
		loadFavorites: () => loadFavorites(true),
		loadMore,
		refresh,
		loadUserFavorite,
		loadFavoriteState,
	};
};

// Hook for managing user's favorites across all POIs
export const useUserFavorites = ({
	user_id,
	limit = 20,
	offset = 0,
	favorite_type,
}: UseUserFavoritesOptions = {}): UseUserFavoritesResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State
	const [favorites, setFavorites] = useState<POIFavoriteInteraction[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(false);
	const [totalCount, setTotalCount] = useState(0);
	const [currentOffset, setCurrentOffset] = useState(offset);

	// Load user's favorites
	const loadFavorites = useCallback(
		async (userId?: string, resetOffset: boolean = false) => {
			const userIdToUse = userId || currentUserId;
			if (!userIdToUse) return;

			setLoading(true);
			setError(null);

			const offsetToUse = resetOffset ? 0 : currentOffset;

			try {
				const response = await FavoritesService.getUserFavorites(
					userIdToUse,
					limit,
					offsetToUse,
					favorite_type
				);

				if (response.success) {
					// Cast to POIFavoriteInteraction since we know these are favorites
					const favoriteInteractions = (response.interactions ||
						[]) as POIFavoriteInteraction[];

					if (resetOffset || offsetToUse === 0) {
						setFavorites(favoriteInteractions);
					} else {
						setFavorites((prev) => [...prev, ...favoriteInteractions]);
					}

					setTotalCount(response.total_count || 0);
					setHasMore(response.has_more || false);
					setCurrentOffset(offsetToUse + favoriteInteractions.length);
				} else {
					throw new Error(response.error || 'Failed to load favorites');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to load favorites';
				setError(errorMessage);
				console.error('Error loading favorites:', err);
			} finally {
				setLoading(false);
			}
		},
		[currentUserId, currentOffset, limit, favorite_type]
	);

	// Remove favorite
	const removeFavorite = useCallback(
		async (favoriteId: string | number) => {
			if (!currentUserId) return;

			try {
				// Find the favorite to get POI identifier
				const favorite = favorites.find(
					(fav) => fav.favorite_id === favoriteId
				);
				if (!favorite) {
					throw new Error('Favorite not found');
				}

				const poi: POIIdentifier = {
					poi_id: favorite.poi_id,
					user_poi_temp_id: favorite.user_poi_temp_id,
					user_poi_approved_id: favorite.user_poi_approved_id,
					poi_type: favorite.poi_type,
				};

				const response = await FavoritesService.removeFavorite(poi);

				if (response.success) {
					// Remove from local state
					setFavorites((prev) =>
						prev.filter((fav) => fav.favorite_id !== favoriteId)
					);
					setTotalCount((prev) => Math.max(0, prev - 1));
				} else {
					throw new Error(response.error || 'Failed to remove favorite');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to remove favorite';
				setError(errorMessage);
				console.error('Error removing favorite:', err);
			}
		},
		[currentUserId, favorites]
	);

	// Load more favorites
	const loadMore = useCallback(async () => {
		if (!hasMore || loading) return;
		await loadFavorites(undefined, false);
	}, [hasMore, loading, loadFavorites]);

	// Refresh favorites
	const refresh = useCallback(async () => {
		setCurrentOffset(0);
		await loadFavorites(undefined, true);
	}, [loadFavorites]);

	// Auto-load on mount
	useEffect(() => {
		if (currentUserId) {
			// Add a small delay to prevent rapid successive calls
			const timeoutId = setTimeout(() => {
				refresh();
			}, 100);

			return () => clearTimeout(timeoutId);
		}
	}, [currentUserId]); // Remove refresh from dependencies to prevent infinite loop

	return {
		favorites,
		loading,
		error,
		hasMore,
		totalCount,
		loadFavorites: (userId?: string) => loadFavorites(userId, true),
		loadMore,
		refresh,
		removeFavorite,
	};
};

// === UNIFIED SAVES HOOK (Legacy API compatibility) ===

export const useSaves = ({
	poi_identifier,
	user_id,
	enable_optimistic_updates = true,
}: UseSavesOptions = {}): UseSavesResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State
	const [saveCount, setSaveCount] = useState(0);
	const [isSaved, setIsSaved] = useState(false);
	const [loading, setLoading] = useState(false);
	const [actionLoading, setActionLoading] = useState(false);
	const [ready, setReady] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Load save state for the POI
	const loadSaveState = useCallback(async () => {
		if (!poi_identifier) return;
		setLoading(true);
		setError(null);
		try {
			if (currentUserId) {
				// Load user-specific save status
				const response = await FavoritesService.getUserSaveStatus(
					poi_identifier,
					currentUserId
				);

				if (response.success) {
					setIsSaved(response.is_saved || false);
					setSaveCount(response.save_count || 0);
					setReady(true);
					console.log('Loaded save state:', {
						isSaved: response.is_saved,
						saveCount: response.save_count,
					});
				} else {
					throw new Error('Failed to load save status');
				}
			} else {
				// Load public save count only (no user authentication)
				const response = await FavoritesService.getPOISaveCount(poi_identifier);

				if (response.success) {
					setIsSaved(false); // No user, so not saved
					setSaveCount(response.saveCount || 0);
					setReady(true);
					console.log('Loaded public save count:', response.saveCount);
				} else {
					throw new Error('Failed to load save count');
				}
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to load save data';
			setError(errorMessage);
			console.error('Error loading save state:', err);
		} finally {
			setLoading(false);
		}
	}, [poi_identifier, currentUserId]);

	// Toggle save
	const toggleSave = useCallback(
		async (notes?: string) => {
			setActionLoading(true);
			setError(null);
			try {
				if (!poi_identifier) throw new Error('POI identifier is required');
				await FavoritesService.toggle(poi_identifier, isSaved, notes);
				// handle response as needed
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to toggle save';
				setError(errorMessage);
				console.error('Error toggling save:', err);
				throw err;
			} finally {
				setActionLoading(false);
			}
		},
		[poi_identifier, isSaved]
	);

	// Add save
	const addSave = useCallback(
		async (notes?: string) => {
			setActionLoading(true);
			setError(null);
			try {
				if (!poi_identifier) throw new Error('POI identifier is required');
				await FavoritesService.add(poi_identifier, notes);
				// handle response as needed
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to add save';
				setError(errorMessage);

				// Revert optimistic update
				if (enable_optimistic_updates) {
					setIsSaved(false);
					setSaveCount((prev) => Math.max(0, prev - 1));
				}

				console.error('Error adding save:', err);
			} finally {
				setActionLoading(false);
			}
		},
		[poi_identifier]
	);

	// Remove save
	const removeSave = useCallback(async () => {
		setActionLoading(true);
		setError(null);
		try {
			if (!poi_identifier) throw new Error('POI identifier is required');
			await FavoritesService.remove(poi_identifier);
			// handle response as needed
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to remove save';
			setError(errorMessage);

			// Revert optimistic update
			if (enable_optimistic_updates) {
				setIsSaved(true);
				setSaveCount((prev) => prev + 1);
			}

			console.error('Error removing save:', err);
		} finally {
			setActionLoading(false);
		}
	}, [poi_identifier]);

	// Update save notes
	const updateSaveNotes = useCallback(
		async (notes: string) => {
			setActionLoading(true);
			setError(null);
			try {
				if (!isSaved) {
					await addSave(notes);
				} else {
					// For now, we'll remove and re-add with new notes
					await removeSave();
					await addSave(notes);
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to update save notes';
				setError(errorMessage);
				console.error('Error updating save notes:', err);
			} finally {
				setActionLoading(false);
			}
		},
		[isSaved, addSave, removeSave]
	);

	// Auto-load on mount and when dependencies change
	useEffect(() => {
		if (!poi_identifier) {
			setError('POI identifier is missing. Cannot load saves.');
			setReady(false);
			return;
		}
		if (true) {
			const timeoutId = setTimeout(() => {
				loadSaveState();
			}, 100);
			return () => clearTimeout(timeoutId);
		}
	}, [poi_identifier, loadSaveState]);

	return {
		saveCount,
		isSaved,
		loading,
		actionLoading,
		ready,
		error,
		toggleSave,
		addSave,
		removeSave,
		updateSaveNotes,
		refresh: loadSaveState,
		loadSaveState,
	};
};
