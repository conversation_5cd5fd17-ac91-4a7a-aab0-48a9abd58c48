/** @format */

'use client';

import { LikesService } from '@/app/shared/userInteractions/likes/services';
import {
	POILikeInteraction,
	UseLikesOptions,
	UseLikesResult,
	UseUserLikesOptions,
	UseUserLikesResult,
} from '@/app/shared/userInteractions/likes/types';
import { useSession } from 'next-auth/react';
import { useCallback, useEffect, useState } from 'react';

export const useLikes = ({
	poi_identifier,
	user_id,
	auto_load = true,
	enable_optimistic_updates = true,
}: UseLikesOptions = {}): UseLikesResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State
	const [likeCount, setLikeCount] = useState(0);
	const [isLiked, setIsLiked] = useState(false);
	const [loading, setLoading] = useState(false);
	const [actionLoading, setActionLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [ready, setReady] = useState(false);

	// Load like state for the POI
	const loadLikeState = useCallback(async () => {
		if (!poi_identifier) return;

		setLoading(true);
		setError(null);

		try {
			if (currentUserId) {
				// Load user-specific like status
				const response = await LikesService.getUserLikeStatus(
					poi_identifier,
					currentUserId
				);

				if (response.success) {
					setIsLiked(response.isLiked);
					setLikeCount(response.likeCount || 0);
					setReady(true);
					console.log('Loaded like state:', {
						isLiked: response.isLiked,
						likeCount: response.likeCount,
					});
				} else {
					throw new Error('Failed to load like status');
				}
			} else {
				// Load public like count only (no user authentication)
				const response = await LikesService.getPOILikeCount(poi_identifier);

				if (response.success) {
					setIsLiked(false); // No user, so not liked
					setLikeCount(response.likeCount || 0);
					setReady(true);
					console.log('Loaded public like count:', response.likeCount);
				} else {
					throw new Error('Failed to load like count');
				}
			}
		} catch (err) {
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to load like data';
			setError(errorMessage);
			console.error('Error loading like state:', err);
		} finally {
			setLoading(false);
		}
	}, [poi_identifier, currentUserId]);

	// Add like
	const addLike = useCallback(async () => {
		if (!poi_identifier || !currentUserId) {
			setError('POI identifier and user ID are required');
			return;
		}
		if (!ready || actionLoading) {
			console.log('Not ready or already loading, skipping');
			return;
		}

		// NOTE: Daily limit checks removed - likes are now unlimited

		setActionLoading(true);
		setError(null);
		// Optimistic update
		if (enable_optimistic_updates) {
			setIsLiked(true);
			setLikeCount((prev) => prev + 1);
		}
		try {
			const response = await LikesService.addLike(poi_identifier);
			if (response.success) {
				if (!enable_optimistic_updates) {
					setIsLiked(true);
					setLikeCount(response.like_count || likeCount + 1);
				}
				// Don't auto-refresh to prevent duplicate requests
				// The optimistic update already handles the UI state
			} else {
				throw new Error(response.error || 'Failed to add like');
			}
		} catch (err) {
			if (enable_optimistic_updates) {
				setIsLiked(false);
				setLikeCount((prev) => Math.max(0, prev - 1));
			}
			const errorMessage =
				err instanceof Error ? err.message : 'Failed to add like';
			setError(errorMessage);
			console.error('Error adding like:', err);
		} finally {
			setActionLoading(false);
		}
	}, [
		poi_identifier,
		currentUserId,
		ready,
		actionLoading,
		isLiked,
		likeCount,
		enable_optimistic_updates,
		loadLikeState,
	]);

	// Remove like
	const removeLike = useCallback(async () => {
		if (!poi_identifier || !currentUserId) {
			setError('POI identifier and user ID are required');
			return;
		}
		if (!ready || actionLoading) {
			console.log('Not ready or already loading, skipping');
			return;
		}
		setActionLoading(true);
		setError(null);
		if (enable_optimistic_updates) {
			setIsLiked(false);
			setLikeCount((prev) => Math.max(0, prev - 1));
		}
		try {
			const response = await LikesService.removeLike(poi_identifier);
			if (response.success) {
				if (!enable_optimistic_updates) {
					setIsLiked(false);
					setLikeCount(response.like_count || Math.max(0, likeCount - 1));
				}
				// Don't auto-refresh to prevent duplicate requests
				// The optimistic update already handles the UI state
			} else {
				throw new Error(response.error || 'Failed to remove like');
			}
		} catch (err) {
			if (enable_optimistic_updates) {
				setIsLiked(true);
				setLikeCount((prev) => prev + 1);
			}
			setError(err instanceof Error ? err.message : 'Failed to remove like');
			console.error('Error removing like:', err);
		} finally {
			setActionLoading(false);
		}
	}, [
		poi_identifier,
		currentUserId,
		ready,
		actionLoading,
		isLiked,
		likeCount,
		enable_optimistic_updates,
		loadLikeState,
	]);

	// Toggle like
	const toggleLike = useCallback(async () => {
		if (!ready || actionLoading) {
			console.log('Not ready or already loading, skipping toggle');
			return;
		}
		if (isLiked) {
			await removeLike();
		} else {
			await addLike();
		}
	}, [ready, actionLoading, isLiked, addLike, removeLike]);

	// Initial load effect (like useVisits)
	useEffect(() => {
		let cancelled = false;
		if (!poi_identifier) {
			setLoading(false);
			setReady(false);
			setError('POI identifier is missing.');
			return;
		}

		// Only auto-load if auto_load is enabled
		if (!auto_load) {
			setLoading(false);
			setReady(false);
			return;
		}

		setLoading(true);
		setReady(false);
		setError(null);
		const loadAll = async () => {
			try {
				// Single API call to get both user status and total count
				if (currentUserId) {
					// Get user-specific status (includes total count)
					const response = await LikesService.getUserLikeStatus(
						poi_identifier,
						currentUserId
					);
					if (response.success) {
						setIsLiked(response.isLiked);
						setLikeCount(response.likeCount || 0);
					} else {
						// Fallback: get public count only
						setIsLiked(false);
						const countResponse = await LikesService.getPOILikeCount(
							poi_identifier
						);
						setLikeCount(
							countResponse.success ? countResponse.likeCount || 0 : 0
						);
					}
				} else {
					// Get public count only (no user session)
					const response = await LikesService.getPOILikeCount(poi_identifier);
					if (response.success) {
						setIsLiked(false);
						setLikeCount(response.likeCount || 0);
					} else {
						setIsLiked(false);
						setLikeCount(0);
					}
				}
			} catch (error) {
				console.error('Error loading likes:', error);
				if (!cancelled) {
					setError('Failed to load likes');
					// Set default values on error
					setIsLiked(false);
					setLikeCount(0);
				}
			} finally {
				if (!cancelled) {
					setLoading(false);
					setReady(true); // Always set ready to true in finally block like visits hook
				}
			}
		};
		loadAll();
		return () => {
			cancelled = true;
		};
	}, [poi_identifier, currentUserId, auto_load]);

	// Note: Removed duplicate auto_load effect since we now have the initial load effect that works like useVisits

	return {
		likeCount,
		isLiked,
		loading,
		actionLoading,
		ready,
		error,
		toggleLike,
		addLike,
		removeLike,
		loadLikeState,
		refresh: loadLikeState,
	};
};

// Hook for managing user's likes across all POIs
export const useUserLikes = ({
	user_id,
	auto_load = true,
	limit = 20,
	offset = 0,
}: UseUserLikesOptions = {}): UseUserLikesResult => {
	const { data: session } = useSession();
	const currentUserId = user_id || session?.user?.id;

	// State
	const [likes, setLikes] = useState<POILikeInteraction[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(false);
	const [totalCount, setTotalCount] = useState(0);
	const [currentOffset, setCurrentOffset] = useState(offset);

	// Load user's likes
	const loadLikes = useCallback(
		async (
			userId?: string,
			resetOffset: boolean = false,
			customOffset?: number
		) => {
			const userIdToUse = userId || currentUserId;
			if (!userIdToUse) return;

			setLoading(true);
			setError(null);

			const offsetToUse =
				customOffset !== undefined
					? customOffset
					: resetOffset
					? 0
					: currentOffset;

			try {
				const response = await LikesService.getUserLikes(
					userIdToUse,
					limit,
					offsetToUse
				);

				if (response.success) {
					// Cast to POILikeInteraction since we know these are likes
					const likeInteractions =
						response.interactions as POILikeInteraction[];

					if (resetOffset || offsetToUse === 0) {
						setLikes(likeInteractions);
					} else {
						setLikes((prev) => [...prev, ...likeInteractions]);
					}

					setTotalCount(response.total_count || 0);
					setHasMore(response.has_more || false);
					setCurrentOffset(offsetToUse + likeInteractions.length);
				} else {
					throw new Error(response.error || 'Failed to load likes');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to load likes';
				setError(errorMessage);
				console.error('Error loading likes:', err);
			} finally {
				setLoading(false);
			}
		},
		[currentUserId, limit]
	);

	// Load more likes
	const loadMore = useCallback(async () => {
		if (!hasMore || loading) return;
		await loadLikes(undefined, false, currentOffset);
	}, [hasMore, loading, loadLikes, currentOffset]);

	// Refresh likes
	const refresh = useCallback(async () => {
		setCurrentOffset(0);
		await loadLikes(undefined, true, 0);
	}, [loadLikes]);

	// Remove a like
	const removeLike = useCallback(
		async (likeId: string | number) => {
			try {
				// Find the like to remove
				const likeToRemove = likes.find((like) => like.id === likeId);
				if (!likeToRemove) {
					throw new Error('Like not found');
				}

				// Convert to POI identifier
				const poiIdentifier = {
					poi_type: likeToRemove.poi_type,
					poi_id: likeToRemove.poi_id,
					user_poi_temp_id: likeToRemove.user_poi_temp_id,
					user_poi_approved_id: likeToRemove.user_poi_approved_id,
				};

				const response = await LikesService.removeLike(poiIdentifier);

				if (response.success) {
					// Remove from local state
					setLikes((prev) => prev.filter((like) => like.id !== likeId));
					setTotalCount((prev) => Math.max(0, prev - 1));
				} else {
					throw new Error(response.error || 'Failed to remove like');
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : 'Failed to remove like';
				setError(errorMessage);
				console.error('Error removing like:', err);
				throw err;
			}
		},
		[likes]
	);

	// Auto-load on mount
	useEffect(() => {
		if (auto_load && currentUserId) {
			loadLikes(currentUserId, true);
		}
	}, [auto_load, currentUserId]);

	return {
		likes,
		loading,
		error,
		hasMore,
		totalCount,
		loadLikes,
		loadMore,
		refresh,
		removeLike,
	};
};
